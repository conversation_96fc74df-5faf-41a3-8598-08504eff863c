{"name": "mySchema", "description": "mySchema", "strict": true, "schema": {"type": "object", "properties": {"knowledgePointName": {"type": "string", "description": "知识点名称"}, "questions": {"type": "array", "items": {"type": "object", "description": "知识点下的例题", "properties": {"question": {"type": "object", "description": "例题", "properties": {"content": {"type": "string", "description": "题目中的文字内容"}, "images": {"type": "array", "items": {"type": "string", "description": "题目中的图片"}}}}, "answer": {"type": "object", "description": "答案", "properties": {"content": {"type": "string", "description": "答案中的文字内容"}, "images": {"type": "array", "items": {"type": "string", "description": "答案中的图片"}}}}, "thinking": {"type": "object", "description": "解题思路", "properties": {"content": {"type": "string", "description": "解题思路中的文字内容"}, "images": {"type": "array", "items": {"type": "string", "description": "解题思路中的图片"}}}}}}}}, "required": ["knowledgePointName"]}}