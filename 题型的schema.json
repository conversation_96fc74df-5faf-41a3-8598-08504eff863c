{"name": "mySchema", "description": "mySchema", "strict": true, "schema": {"type": "object", "properties": {"keyPointName": {"type": "string", "description": "题型或者类型名称"}, "category": {"type": "string", "description": "题型或者类型的类型", "enum": ["题型", "类型"]}, "sortNo": {"type": "integer", "description": "题型或者类型的序号"}, "questions": {"type": "array", "description": "例题", "items": {"type": "object", "properties": {"questionContent": {"type": "string", "description": "题目中的文字内容，请保持原始Markdown文本内容，包含的LaTeX数学表达式（如\\mathrm等）需保持原样，其中的反斜杠\\无需额外转义"}, "questionImages": {"type": "array", "items": {"type": "string", "description": "题目中的图片"}}, "answerContent": {"type": "string", "description": "答案中的文字内容，请保持原始Markdown文本内容，包含的LaTeX数学表达式（如\\mathrm等）需保持原样，其中的反斜杠\\无需额外转义"}, "answerImages": {"type": "array", "items": {"type": "string", "description": "答案中的图片"}}, "thinkingContent": {"type": "string", "description": "解题思路或者解题步骤中的文字内容，请保持原始Markdown文本内容，包含的LaTeX数学表达式（如\\mathrm等）需保持原样，其中的反斜杠\\无需额外转义"}, "thinkingImages": {"type": "array", "items": {"type": "string", "description": "解题思路或者解题步骤中的图片"}}}}}}, "required": ["keyPointName", "sortNo", "category", "questions"]}}