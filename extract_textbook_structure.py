import os
import json
import re
from pathlib import Path
import sys
import zipfile

def extract_zip_if_needed(zip_path):
    """
    检查zip文件是否需要解压，如果需要则解压

    Args:
        zip_path (Path): zip文件路径

    Returns:
        bool: 是否进行了解压操作
    """
    # 获取zip文件的目录名（去掉.zip后缀）
    expected_dir_name = zip_path.stem
    expected_dir_path = zip_path.parent / expected_dir_name

    # 检查是否已存在同名目录
    if expected_dir_path.exists() and expected_dir_path.is_dir():
        print(f"目录 {expected_dir_name} 已存在，跳过解压")
        return False

    # 解压zip文件
    try:
        # 创建目标目录
        expected_dir_path.mkdir(exist_ok=True)

        # 解压到目标目录
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(expected_dir_path)
        print(f"成功解压 {zip_path.name} 到 {expected_dir_name}")
        return True
    except Exception as e:
        print(f"解压 {zip_path.name} 时出错: {e}")
        # 如果解压失败，清理可能创建的空目录
        if expected_dir_path.exists() and expected_dir_path.is_dir():
            try:
                expected_dir_path.rmdir()
            except:
                pass
        return False

def extract_textbook_structure(directory_path):
    """
    从指定目录提取教材结构信息
    
    Args:
        directory_path (str): 教材目录路径
    
    Returns:
        dict: 包含教材名称和章节信息的字典
    """
    directory = Path(directory_path)
    
    if not directory.exists():
        raise FileNotFoundError(f"目录不存在: {directory_path}")
    
    # 提取书名
    textbook_name = None
    for file in directory.iterdir():
        if file.name.endswith("主书.pdf"):
            # 从文件名中提取书名（去掉.pdf后缀）
            textbook_name = file.stem.strip()
            break
    
    # 如果没找到主书.pdf，尝试从metainfo.txt中获取
    if not textbook_name:
        metainfo_path = directory / "主书" / "metainfo.txt"
        if metainfo_path.exists():
            with open(metainfo_path, 'r', encoding='utf-8') as f:
                first_line = f.readline().strip()
                textbook_name = first_line
    
    if not textbook_name:
        textbook_name = f"未知教材 - {directory.name}"
    
    # 提取章节信息
    chapters = []
    chapter_dict = {}  # 用于去重，以章节号为键

    # 在"主书"子目录中查找章节文件
    main_book_dir = directory / "主书"
    if main_book_dir.exists() and main_book_dir.is_dir():
        # 首先处理所有的zip文件，检查是否需要解压
        zip_files = [item for item in main_book_dir.iterdir()
                    if item.name.endswith('.zip') and re.match(r'^\d+', item.name)]

        for zip_file in zip_files:
            extract_zip_if_needed(zip_file)

        # 查找数字开头的.zip文件或文件夹
        for item in main_book_dir.iterdir():
            # 匹配模式：数字开头，可能包含中文标题
            if re.match(r'^\d+', item.name):
                if item.name.endswith('.zip') or item.is_dir():
                    # 提取章节号和章节名
                    match = re.match(r'^(\d+)、(.+?)(?:\.zip)?$', item.name)
                    if match:
                        sort_no = match.group(1)
                        chapter_name = match.group(2)

                        # 使用字典去重，同一章节号只保留一个
                        if sort_no not in chapter_dict:
                            chapter_dict[sort_no] = {
                                "chapterName": chapter_name,
                                "sortNo": sort_no
                            }

    # 将字典转换为列表
    chapters = list(chapter_dict.values())
    
    # 按章节号排序
    chapters.sort(key=lambda x: int(x["sortNo"]))
    
    return {
        "textbook": textbook_name,
        "chapters": chapters
    }

def main(directory_path):
    # 示例目录路径
    # directory_path = "/Users/<USER>/WorkSpace/小学数学/人教/三年级上册"
    
    try:
        result = extract_textbook_structure(directory_path)
        
        # 输出JSON格式
        json_output = json.dumps(result, ensure_ascii=False, indent=2)
        print("提取的教材结构：")
        print(json_output)
        
        # 保存到文件
        output_file = directory_path.rsplit('/', 1)[-1] + "textbook_structure.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main(sys.argv[1])