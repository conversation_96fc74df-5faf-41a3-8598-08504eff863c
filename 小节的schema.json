{"name": "mySchema", "description": "mySchema", "strict": true, "schema": {"type": "object", "properties": {"sectionName": {"type": "string", "description": "小节名称，提取出小节序号后面的文本内容"}, "sortNo": {"type": "integer", "description": "小节序号，通常是“第X节”这样的形式，提取出X的值"}, "keyPoints": {"type": "array", "items": {"type": "object", "properties": {"keyPointName": {"type": "string", "description": "关键点名称，以### 开头的都是关键点"}, "category": {"type": "string", "description": "关键点类型", "enum": ["知识点", "易错点", "疑难点", "方法"]}, "sortNo": {"type": "integer", "description": "排序"}, "questions": {"type": "array", "description": "例题", "items": {"type": "object", "properties": {"questionContent": {"type": "string", "description": "题目中的文字内容，请保持原始Markdown文本内容，包含的LaTeX数学表达式（如\\mathrm等）需保持原样，其中的反斜杠\\无需额外转义"}, "questionImages": {"type": "array", "items": {"type": "string", "description": "题目中的图片"}}, "answerContent": {"type": "string", "description": "答案中的文字内容，请保持原始Markdown文本内容，包含的LaTeX数学表达式（如\\mathrm等）需保持原样，其中的反斜杠\\无需额外转义"}, "answerImages": {"type": "array", "items": {"type": "string", "description": "答案中的图片"}}, "thinkingContent": {"type": "string", "description": "解题思路或者解题步骤中的文字内容，请保持原始Markdown文本内容，包含的LaTeX数学表达式（如\\mathrm等）需保持原样，其中的反斜杠\\无需额外转义"}, "thinkingImages": {"type": "array", "items": {"type": "string", "description": "解题思路或者解题步骤中的图片"}}}}}}}}}, "required": ["sectionName", "sortNo", "keyPoints"]}}